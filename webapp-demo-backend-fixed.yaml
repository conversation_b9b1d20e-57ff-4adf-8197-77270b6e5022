apiVersion: apps/v1
kind: Deployment
metadata:
  name: webapp-demo-backend
  namespace: tenant-webapp-demo
spec:
  replicas: 2
  selector:
    matchLabels:
      app: webapp-demo-backend
  template:
    metadata:
      labels:
        app: webapp-demo-backend
        tenant: webapp-demo
        component: backend
    spec:
      initContainers:
      - name: init-storage
        image: busybox
        command:
        - /bin/sh
        - -c
        - |
          mkdir -p /storage/ArchAssets/public
          mkdir -p /storage/ArchAssets/config/autoload
          mkdir -p /storage/ArchAssets/logs
          mkdir -p /storage/ArchAssets/cache
          chmod -R 777 /storage
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
      - name: copy-webapp-files
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        command:
        - /bin/sh
        - -c
        - |
          echo "Copying REAL webapp files to shared storage..."
          if [ -d "/var/www/html" ]; then
            echo "Found REAL webapp files in /var/www/html"
            cp -r /var/www/html/* /storage/ArchAssets/ 2>/dev/null || true
            echo "Real application files copied from /var/www/html"
            ls -la /storage/ArchAssets/ | head -10
          fi
          if [ -d "/app" ]; then
            echo "Found webapp files in /app"
            cp -r /app/* /storage/ArchAssets/ 2>/dev/null || true
          fi
          if [ -d "/usr/src/app" ]; then
            echo "Found webapp files in /usr/src/app"
            cp -r /usr/src/app/* /storage/ArchAssets/ 2>/dev/null || true
          fi
          # DO NOT OVERWRITE REAL APPLICATION FILES WITH TEST FILES
          # The real Architrave application is now in /storage/ArchAssets
          chmod -R 755 /storage/ArchAssets 2>/dev/null || true
          echo "REAL webapp files copied successfully - NO TEST FILE OVERWRITE"
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
      - name: nginx-config-setup
        image: nginx:1.21-alpine
        command: ["/bin/sh"]
        args:
        - -c
        - |
          echo "Setting up nginx configuration for HTTP..."
          cat > /etc/nginx/conf.d/default.conf << 'EOF'
          server {
              listen 8080;
              server_name localhost;
              root /storage/ArchAssets/public;
              index index.php index.html;

              location /health {
                  access_log off;
                  return 200 "healthy";
                  add_header Content-Type text/plain;
              }

              location /api/ {
                  try_files $uri $uri/ /api/index.php?$query_string;
              }

              location ~ /api/.*\.php$ {
                  try_files $uri =404;
                  fastcgi_split_path_info ^(.+\.php)(/.+)$;
                  fastcgi_pass 127.0.0.1:9000;
                  fastcgi_index index.php;
                  include fastcgi_params;
                  fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                  fastcgi_param PATH_INFO $fastcgi_path_info;
                  fastcgi_read_timeout 300;
              }

              location ~ \.php$ {
                  try_files $uri =404;
                  fastcgi_split_path_info ^(.+\.php)(/.+)$;
                  fastcgi_pass 127.0.0.1:9000;
                  fastcgi_index index.php;
                  include fastcgi_params;
                  fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                  fastcgi_param PATH_INFO $fastcgi_path_info;
                  fastcgi_read_timeout 300;
              }

              location / {
                  try_files $uri $uri/ /api/index.php?$query_string;
              }
          }
          EOF
          echo "Nginx configuration created"
        volumeMounts:
        - name: nginx-config-volume
          mountPath: /etc/nginx/conf.d
      containers:
      - name: backend
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        ports:
        - containerPort: 9000
          name: php-fpm
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: webapp-demo-db-secret
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: webapp-demo-db-secret
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: webapp-demo-db-secret
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: webapp-demo-db-secret
              key: DB_PASSWORD
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: webapp-demo-db-secret
              key: DB_NAME
        - name: TENANT_ID
          value: "webapp-demo"
        - name: ENVIRONMENT
          value: "production"
        resources:
          limits:
            cpu: 400m
            memory: 512Mi
          requests:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
      - name: nginx
        image: nginx:1.21-alpine
        ports:
        - containerPort: 8080
          name: http
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: storage-volume
          mountPath: /storage
        - name: nginx-config-volume
          mountPath: /etc/nginx/conf.d
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: storage-volume
        emptyDir: {}
      - name: nginx-config-volume
        emptyDir: {}
